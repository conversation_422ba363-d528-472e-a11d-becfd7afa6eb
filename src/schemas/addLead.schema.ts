import { type InferInput, object, pipe, string } from "valibot";

const messages = {
  name: "Name is required",
  sourceChannel: "SourceChannel is required",
};

export const addLeadSchema: ReturnType<typeof object> = object({
  contactInfo: string(),
  followUpDate: string(),
  followUpStatus: string(),
  name: pipe(string(), require(messages.name)),
  note: string(),
  qualification: string(),
  servicesInterest: string(),
  sourceChannel: pipe(string(), require(messages.sourceChannel)),
  startDate: string(),
});

export type AddLeadSchema = InferInput<typeof addLeadSchema>;
